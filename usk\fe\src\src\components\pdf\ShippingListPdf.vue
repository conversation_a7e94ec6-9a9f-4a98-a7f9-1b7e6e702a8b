<template>
  <div class="tw:font-pdfSans">
    <div class="tw:text-black">
      <div id="large-body-pdf">
        <div :style="{ textAlign: 'center', fontSize: '30px', fontWeight: 700 }">出荷実績</div>
        <div>
          <div
            :style="{
              paddingTop: '4px',
              paddingBottom: '4px',
              fontSize: '14px',
            }"
          >
            作成日：
            <span>{{ FORMAT_DATE_JAPAN() }}</span>
          </div>
          <div
            :style="{
              paddingTop: '4px',
              paddingBottom: '4px',
              fontSize: '14px',
            }"
          >
            作成者：{{ profile?.enterprise?.enterprise_name }}
          </div>
          <div
            :style="{
              paddingTop: '4px',
              paddingBottom: '4px',
              fontSize: '14px',
            }"
          >
            実績表示期間：
            <span :style="{ fontSize: '14px' }">{{
              formDataExport?.startDate ? FORMAT_DATE_JAPAN(formDataExport?.startDate) : ''
            }}</span>
            <span
              :style="{ fontSize: '14px' }"
              v-if="formDataExport?.startDate || formDataExport?.endDate"
              >～</span
            >
            <span :style="{ fontSize: '14px' }">{{
              formDataExport?.endDate ? FORMAT_DATE_JAPAN(formDataExport?.endDate) : ''
            }}</span>
          </div>
          <div
            :style="{
              paddingTop: '4px',
              paddingBottom: '4px',
              fontSize: '14px',
            }"
          >
            検索条件：
            <span :style="{ fontSize: '14px' }"> {{ truncateText(formDataExport.code || '', 7) }}</span>
            <span
              :style="{ fontSize: '14px' }"
              v-if="
                formDataExport?.code &&
                (formDataExport?.licenseNumber || formDataExport?.note1 || formDataExport?.note2)
              "
              >、</span
            >
            <span :style="{ fontSize: '14px' }"> {{ truncateText(formDataExport.licenseNumber || '', 11) }}</span>
            <span
              :style="{ fontSize: '14px' }"
              v-if="
                formDataExport.licenseNumber && (formDataExport?.note1 || formDataExport?.note2)
              "
              >、</span
            >
            <!-- 備考1 note1 -->
            <span :style="{ fontSize: '14px' }"> {{ truncateText(formDataExport?.note1 || '', 16) }}</span>
            <span
              :style="{ fontSize: '14px' }"
              v-if="formDataExport?.note1 && formDataExport?.note2"
              >、</span
            >
            <!-- 備考2 note2 -->
            <span :style="{ fontSize: '14px' }"> {{ truncateText(formDataExport?.note2 || '', 16) }}</span>
          </div>
        </div>
        <div
          class="tw:flex tw:w-full tw:mt-7"
          :style="{ pageBreakInside: 'avoid', fontWeight: 700 }"
        >
          <div class="row-item" :style="{ textAlign: 'center', width: '82px' }">出荷日</div>
          <div class="row-item" :style="{ textAlign: 'center', width: '212px' }">
            出荷者<span
              :style="{
                fontWeight: 400,
              }"
              >※事業者</span
            >
          </div>
          <div class="row-item" :style="{ textAlign: 'center', width: '152px' }">許可番号</div>
          <div class="row-item" :style="{ textAlign: 'center', width: '150px' }">
            漁獲番号/荷口番号
          </div>
          <div class="row-item" :style="{ textAlign: 'center', width: '212px' }">
            出荷先(届出事業者)
          </div>
          <div class="row-item" :style="{ textAlign: 'center', width: '90px' }">出荷量[g]</div>
          <div class="row-item" :style="{ textAlign: 'center', width: '212px' }">差異の理由</div>
        </div>
        <div
          v-for="(item, index) in dataAll"
          :key="index"
          class="tw:flex tw:w-full"
          :style="{ pageBreakInside: 'avoid' }"
        >
          <div class="row-item" :style="{ textAlign: 'left', width: '82px' }">
            {{ FORMAT_DATE(item.shipping_date) }}
          </div>
          <div class="row-item" :style="{ textAlign: 'left', width: '212px' }">
            {{ `${item.name}${item.mark_staff}` }}
          </div>
          <div class="row-item" :style="{ textAlign: 'left', width: '152px' }">
            {{ item.license_number }}
          </div>
          <div class="row-item" :style="{ textAlign: 'left', width: '150px' }">
            {{ item.code }}
          </div>
          <div class="row-item" :style="{ textAlign: 'left', width: '212px' }">
            {{ item.enterprise_name }}
          </div>
          <div class="row-item" :style="{ textAlign: 'right', width: '90px' }">
            {{ FORMAT_NUMBER(item.shipping_net_weight, 2) }}
          </div>
          <div class="row-item" :style="{ textAlign: 'left', width: '212px' }">
            {{ item.reason_diff }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, onMounted, ref } from 'vue';
import { FORMAT_DATE, FORMAT_NUMBER, FORMAT_DATE_JAPAN } from 'helpers/common';
// Helper function to truncate text with ellipsis
const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength) + '...';
};
import profileService from 'services/profile.service';

const dataProp = inject('shippingListDataExportPdf');
const formDataExport = inject('formDataExport');
const profile = ref({});
const dataAll = computed(() => dataProp.value || []);

onMounted(async () => {
  const result = await profileService.getProfile();
  profile.value = result.payload.data;
});
</script>

<style scoped>
.row-item {
  border: 1px solid #000;
  padding-left: 8px !important;
  padding-right: 8px !important;
  padding-top: 4px !important;
  padding-bottom: 16px !important;
  vertical-align: middle;
  font-size: 12px !important;
}
</style>
