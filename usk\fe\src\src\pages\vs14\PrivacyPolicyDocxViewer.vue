<template>
  <div class="tw:mt-2 tw:tl:px-3 tw:h-full" id="header">
      <q-card class="tw:p-4 tw:mt-4 tw:mb-4 tw:bg-white">
        <span class="tw:text-l-design tw:font-bold">プライバシーポリシー</span>
    <iframe :src="srcPolicy"
      frameborder='0'
      class="tw:w-full tw:h-[60vh]"
      scrolling="no">
    </iframe>
      </q-card>
  </div>
  <!-- end content -->
  <q-footer
    elevated
    class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
    tw:w-full tw:items-center tw:flex tw:justify-center tw:tl:justify-between
    tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
  >
    <BaseButton
      outline
      class="tw:rounded-[40px]"
      :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
      label="トップに戻る"
      @click.prevent="goToPage('home')"
    />
  </q-footer>
</template>
<script setup>
// open page denso
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
// ===== REF =====
const srcPolicy = ref('');

const goToPage = name => {
  router.push({ name });
};
onMounted(() => {
  srcPolicy.value = `https://view.officeapps.live.com/op/embed.aspx?src=${process.env.CLOUDFRONT}/document/usersite/policy.docx`;
});
</script>
