export const makeXML = (
  destinationEnterprise, // string
  destinationUser, // string
  consignmentNumber, // string
  grossWeight, // number
  tareWeight, // number
  arrivalNetWeight, // number
  arrivalDate, // date
  pricePerGram, // string
  price, // string
  startingEnterprise, // string
  startingUser, // string
  licenseNumber, // string
  note1, // string
  numberCopies = 1 // number
) => {
  // if the length of the destination user is less than 16, scale the user name to 2x
  const scaleDestUserName = destinationUser?.length < 16;
  // if the length of the starting user is less than 9, scale the user name to 2x
  const scaleStartingUserName = startingUser?.length < 9;

  let pageContent = '';
  for (let i = 0; i < numberCopies; i++) {
    pageContent += `
      <page>
        <area x="0" y="0" width="550" height="900"/>
        <direction dir="top_to_bottom"/>
        <text dw="true" dh="true"/>
        <position x="10" y="40"/>
        <text>出荷伝票</text>
        <position x="550" y="40"/>
        <text>${arrivalDate.getFullYear()}年</text>
        <position x="680" y="40"/>
        <text>${String(arrivalDate.getMonth() + 1).padStart(2, ' ')}月</text>
        <position x="760" y="40"/>
        <text>${String(arrivalDate.getDate()).padStart(2, ' ')}日</text>
        <text dw="true" dh="true"/>
        <position x="130" y="100"/>
        <text>${destinationEnterprise}</text>
        <position x="10" y="130"/>
        <text dw="false" dh="false"/>
        <text>出荷先</text>
        <position x="135" y="130"/>
        <text>${destinationUser}\u3000様</text>
        <text dw="false" dh="false"/>
        <position x="10" y="195"/>
        <text>漁獲番号</text>
        <text dw="true" dh="true"/>
        <position x="130" y="195"/>
        <text>${consignmentNumber}</text>
        <line x1="130" y1="195" x2="500" y2="195" style="thin"/>
        <text dw="false" dh="false"/>
        <line x1="10" y1="210" x2="840" y2="210" style="medium"/>
        <position x="110" y="250"/>
        <text>品目</text>
        <position x="310" y="250"/>
        <text>全体重量</text>
        <position x="500" y="250"/>
        <text>風袋重量</text>
        <position x="700" y="250"/>
        <text>出荷重量</text>
        <line x1="10" y1="260" x2="840" y2="260" style="thin"/>
        <line x1="10" y1="330" x2="840" y2="330" style="medium"/>
        <position x="95" y="400"/>
        <line x1="260" y1="210" x2="260" y2="330" style="thin"/>
        <line x1="440" y1="210" x2="440" y2="400" style="thin"/>
        <line x1="620" y1="210" x2="620" y2="330" style="thin"/>
        <text dw="true" dh="true"/>
        <position x="280" y="320"/>
        <text>${String(grossWeight).padStart(6, ' ')}g</text>
        <position x="460" y="320"/>
        <text>${String(tareWeight).padStart(6, ' ')}g</text>
        <position x="660" y="320"/>
        <text>${String(arrivalNetWeight).padStart(6, ' ')}g</text>
        <position x="540" y="390"/>
        <text>${price.padStart(11, ' ')}円</text>
        <position x="10" y="320"/>
        <text>シラスウナギ</text>
        <text dw="true" dh="true"/>
        <position x="110" y="390"/>
        <text>合計金額</text>
        <text dw="false" dh="false"/>
        <position x="280" y="390"/>
        <text>(税抜)</text>
        <position x="600" y="430"/>
        <text>(単価：        万円/kg)</text>
        <position x="675" y="430"/>
        <text>${(pricePerGram * 0.1).toFixed(1).padStart(6, ' ')}</text>
        <text dw="true" dh="true"/>
        <line x1="10" y1="400" x2="840" y2="400" style="medium"/>
        <text dw="false" dh="false"/>
        <position x="10" y="480"/>
        <text>許可番号</text>
        <position x="100" y="480"/>
        <text>${licenseNumber}</text>
        <line x1="90" y1="485" x2="330" y2="485" style="thin"/>
        <text dw="false" dh="false"/>
        <position x="10" y="530"/>
        <text>備考</text>
        <position x="100" y="530"/>
        <text>${note1}</text>
        <line x1="90" y1="535" x2="330" y2="535" style="thin"/>
        <text dw="true" dh="true"/>
        <position x="470" y="500"/>
        <text>${startingEnterprise}</text>
        <text dw="false" dh="false"/>
        <position x="380" y="530"/>
        <text>出荷者</text>
        <position x="475" y="530"/>
        <text>${startingUser}</text>
      </page>
      <cut type="feed"/>
    `;
  }

  const data = `
    <epos-print xmlns="http://www.epson-pos.com/schemas/2011/03/epos-print">
      <text lang="ja"/>
      <text smooth="true"/>
      <text font="font_b"/>
      ${pageContent}
    </epos-print>`;

  return data;
};

export const makeQuantityXML = (
  destinationEnterprise, // string
  destinationUser, // string
  consignmentNumber, // string
  quantity, // string
  arrivalDate, // date
  pricePerQuantity, // string
  price, // string
  startingEnterprise, // string
  startingUser, // string
  licenseNumber, // string
  note1, // string
  numberCopies = 1 // number
) => {
  // if the length of the destination user is less than 16, scale the user name to 2x
  const scaleDestUserName = destinationUser?.length < 16;
  // if the length of the starting user is less than 9, scale the user name to 2x
  const scaleStartingUserName = startingUser?.length < 9;

  let pageContent = '';
  for (let i = 0; i < numberCopies; i++) {
    pageContent += `
      <page>
        <area x="0" y="0" width="550" height="900"/>
        <direction dir="top_to_bottom"/>
        <text dw="true" dh="true"/>
        <position x="10" y="40"/>
        <text>出荷伝票</text>
        <position x="550" y="40"/>
        <text>${arrivalDate.getFullYear()}年</text>
        <position x="680" y="40"/>
        <text>${String(arrivalDate.getMonth() + 1).padStart(2, ' ')}月</text>
        <position x="760" y="40"/>
        <text>${String(arrivalDate.getDate()).padStart(2, ' ')}日</text>
        <text dw="true" dh="true"/>
        <position x="130" y="100"/>
        <text>${destinationEnterprise}</text>
        <position x="10" y="130"/>
        <text dw="false" dh="false"/>
        <text>出荷先</text>
        <position x="135" y="130"/>
        <text>${destinationUser}\u3000様</text>
        <text dw="false" dh="false"/>
        <position x="10" y="195"/>
        <text>漁獲番号</text>
        <text dw="true" dh="true"/>
        <position x="130" y="195"/>
        <text>${consignmentNumber}</text>
        <line x1="130" y1="195" x2="500" y2="195" style="thin"/>
        <text dw="false" dh="false"/>
        <line x1="10" y1="210" x2="840" y2="210" style="medium"/>
        <position x="200" y="250"/>
        <text>品目</text>
        <position x="630" y="250"/>
        <text>尾数</text>
        <line x1="10" y1="260" x2="840" y2="260" style="thin"/>
        <line x1="10" y1="330" x2="840" y2="330" style="medium"/>
        <position x="95" y="400"/>
        <line x1="440" y1="210" x2="440" y2="400" style="thin"/>
        <text dw="true" dh="true"/>
        <position x="580" y="320"/>
        <text>${String(quantity).padStart(6, ' ')}尾</text>
        <position x="540" y="390"/>
        <text>${price.padStart(11, ' ')}円</text>
        <position x="100" y="320"/>
        <text>シラスウナギ</text>
        <text dw="true" dh="true"/>
        <position x="110" y="390"/>
        <text>合計金額</text>
        <text dw="false" dh="false"/>
        <position x="280" y="390"/>
        <text>(税抜)</text>
        <position x="600" y="430"/>
        <text>(単価：        円/尾)</text>
        <position x="675" y="430"/>
        <text>${(pricePerQuantity).padStart(6, ' ')}</text>
        <text dw="true" dh="true"/>
        <line x1="10" y1="400" x2="840" y2="400" style="medium"/>
        <text dw="false" dh="false"/>
        <position x="10" y="480"/>
        <text>許可番号</text>
        <position x="100" y="480"/>
        <text>${licenseNumber}</text>
        <line x1="90" y1="485" x2="330" y2="485" style="thin"/>
        <text dw="false" dh="false"/>
        <position x="10" y="530"/>
        <text>備考</text>
        <position x="100" y="530"/>
        <text>${note1}</text>
        <line x1="90" y1="535" x2="330" y2="535" style="thin"/>
        <text dw="true" dh="true"/>
        <position x="470" y="500"/>
        <text>${startingEnterprise}</text>
        <text dw="false" dh="false"/>
        <position x="380" y="530"/>
        <text>出荷者</text>
        <position x="475" y="530"/>
        <text>${startingUser}</text>
      </page>
      <cut type="feed"/>
    `;
  }

  const data = `
    <epos-print xmlns="http://www.epson-pos.com/schemas/2011/03/epos-print">
      <text lang="ja"/>
      <text smooth="true"/>
      <text font="font_b"/>
      ${pageContent}
    </epos-print>`;

  return data;
};

export const makeShippingInfoXML = (
  destinationEnterprise, // string - The parent company name of destination user
  destinationUser, // string - The destination user's name
  consignmentNumber, // string
  shippingNetWeight, // string formatted as '0000,000'
  shippingDate, // date
  shippingInfoURL, // string || undefined (undefined if shipment is arrived)
  startingEnterprise, // string - The parent company name of starting user
  startingUser, // string - The starting user's name
  numberCopies = 1 // number
) => {
  const scaleDestUserName = destinationUser?.length < 16;
  let pageContent = '';

  for (let i = 0; i < numberCopies; i++) {
    pageContent += `
      <page>
        <area x="0" y="0" width="550" height="900"/>
        <direction dir="top_to_bottom"/>
        <position x="10" y="40"/>
        <text dw="true" dh="true"/>
        <text>出荷伝票</text>
        <position x="550" y="40"/>
        <text>${shippingDate.getFullYear()}年</text>
        <position x="680" y="40"/>
        <text>${String(shippingDate.getMonth() + 1).padStart(2, ' ')}月</text>
        <position x="780" y="40"/>
        <text>${String(shippingDate.getDate()).padStart(2, ' ')}日</text>
        <position x="130" y="105"/>
        <text dw="true" dh="true"/>
        <text>${destinationEnterprise}</text>
        <position x="10" y="135"/>
        <text dw="false" dh="false"/>
        <text>出荷先</text>
        <position x="135" y="135"/>
        <text>${destinationUser}\u3000様</text>
        <text dw="false" dh="false"/>
        <position x="10" y="195"/>
        <text>荷口番号</text>
        <text dw="true" dh="true"/>
        <position x="130" y="195"/>
        <text>${consignmentNumber}</text>
        <line x1="130" y1="200" x2="500" y2="200" style="thin"/>
        <line x1="10" y1="210" x2="840" y2="210" style="medium"/>
        <line x1="10" y1="250" x2="840" y2="250" style="thin"/>
        <line x1="10" y1="310" x2="840" y2="310" style="medium"/>
        <line x1="440" y1="210" x2="440" y2="310" style="thin"/>
        <text dw="false" dh="false"/>
        <position x="200" y="240"/>
        <text>品目</text>
        <position x="610" y="240"/>
        <text>出荷重量</text>
        <text dw="true" dh="true"/>
        <position x="110" y="300"/>
        <text>シラスウナギ</text>
        <position x="580" y="300"/>
        <text>${shippingNetWeight.padStart(11, ' ')}</text>
        ${
          shippingInfoURL
            ? `<text dw="false" dh="false"/>
                  <position x="100" y="340"/>
                  <symbol type="qrcode_model_2" level="default" width="9" height="0" size="0">${shippingInfoURL}</symbol>`
            : ''
        }
        <text dw="true" dh="true"/>
        <position x="470" y="500"/>
        <text>${startingEnterprise}</text>
        <text dw="false" dh="false"/>
        <position x="350" y="530"/>
        <text>出荷者</text>
        <position x="475" y="530"/>
        <text>${startingUser}</text>
      </page>
      <cut type="feed"/>
    `;
  }

  const data = `
    <epos-print xmlns="http://www.epson-pos.com/schemas/2011/03/epos-print">
      <text lang="ja"/>
      <text smooth="true"/>
      <text font="font_b"/>
      ${pageContent}
    </epos-print>`;

  return data;
};
