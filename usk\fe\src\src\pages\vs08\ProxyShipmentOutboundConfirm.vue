<template>
  <!-- Because this page has different header title with the other pages
  so we need to render the header title in the page itself
  instead of using the default header title with layout -->
  <div class="tw:h-full tw:flex tw:flex-col tw:transform tw:-translate-y-4">
    <!-- #region header -->
    <div :class="`tw:tl:flex tw:tl:justify-between tw:tl:items-center`">
      <!-- Breadcrumbs -->
      <q-breadcrumbs
        class="tw:text-xs-design tw:font-bold tw:text-blue-3 tw:border-gray tw:py-3 tw:tl:py-0"
        active-color="tw:text-blue-3"
      >
        <template v-slot:separator>
          <q-icon size="1.5em" name="chevron_right" class="tw:text-[#7E8093]" />
        </template>
        <q-breadcrumbs-el
          label="トップ"
          :to="{
            name: 'home',
          }"
        />
        <q-breadcrumbs-el
          class="tw:cursor-pointer"
          label="入荷登録"
          :to="{
            name: 'proxyShipmentOutboundRegistration',
          }"
        />
        <div
          class="tw:ml-2 tw:flex tw:items-center tw:justify-center tw:text-black"
        >
          <span class="tw:text-[#7E8093] tw:pb-1 tw:pr-2">|</span>
          <span>入荷登録入力</span>
        </div>
      </q-breadcrumbs>
      <div
        class="tw:flex tw:items-center tw:justify-between tw:tl:max-w-[60%] tw:py-5 tw:tl:py-0"
      >
        <span
          v-if="proxyUser?.license_number"
          :class="`tw:text-m-design
            tw:mr-6 tw:flex-shrink-0 tw:flex`"
        >
          <span
            class="tw:max-w-[100px] tw:tl:max-w-[200px] tw:truncate tw:block tw:text-[#333333]"
            >{{ proxyUser?.license_number }}</span
          >
        </span>
        <span v-else />
        <span class="tw:font-[700] tw:text-xl-design tw:truncate">{{
          `${proxyUser?.name || ''}`
        }}</span>
      </div>
    </div>
    <!-- #endregion -->
    <q-card
      class="tw:flex-1 tw:p-5 tw:flex tw:flex-col tw:pb-[18rem] tw:tl:pb-[7rem]"
    >
      <h2 :class="`tw:text-xs-design tw:tl:font-bold tw:mb-4`">
        入力内容を確認して「登録する」ボタンを押してください。
      </h2>
      <div
        class="tw:text-[#333333] tw:grid tw:grid-cols-1 tw:gap-y-5 tw:tl:grid-cols-2 tw:gap-x-12 tw:tl:gap-y-2"
      >
        <!-- 漁獲/荷口番号 -->
        <div>
          <BaseLabel label="漁獲/荷口番号" isRequired />
          <div class="tw:font-bold">
            <span class="tw:text-xl-design">
              {{ maskCodeString(displayData?.mainCode + displayData?.subCode) }}
            </span>
          </div>
        </div>
        <!-- 出荷先 -->
        <div>
          <BaseLabel label="出荷先" />
          <div>
            <span class="tw:text-xl-design">
              {{ user?.name || '' }}
            </span>
          </div>
        </div>
        <!-- 出荷日 -->
        <div>
          <BaseLabel label="出荷日" isRequired />
          <div class="tw:font-bold">
            <span class="tw:text-xl-design tw:gap-2 tw:items-center">
              {{ FORMAT_DATE(displayData?.date) }}
            </span>
          </div>
        </div>
        <!-- 出荷登録単位 -->
        <div class="tw:flex tw:items-center tw:tl:block">
          <BaseLabel label="出荷登録単位" />
          <span class="tw:text-s-design tw:tl:hidden">：</span>
          <div class="tw:font-bold">
            <span class="tw:text-xl-design">
              {{ maskVolumeTypeToDisplay(displayData?.volumeType) }}
            </span>
          </div>
        </div>
        <!-- 出荷量 -->
        <div
          v-if="displayData?.volumeType === VOLUME_TYPE_ENUM.WEIGHT"
          class="tw:flex tw:items-center tw:justify-between tw:tl:block"
        >
          <BaseLabel label="出荷量" isRequired />
          <div class="tw:font-bold tw:flex tw:items-center">
            <span class="tw:text-xl-design">
              {{ displayData?.netWeight }}
            </span>
            <span class="tw:text-m-design">g</span>
          </div>
        </div>
        <div
          v-else
          class="tw:flex tw:items-center tw:justify-between tw:tl:block"
        >
          <BaseLabel label="出荷量" isRequired />
          <div class="tw:font-bold tw:flex tw:items-center">
            <span class="tw:text-xl-design">
              {{ displayData?.quantity }}
            </span>
            <span class="tw:text-m-design">尾</span>
          </div>
        </div>
        <!-- 単価 -->
        <div class="">
          <BaseLabel label="単価" />
          <div class="tw:font-bold tw:flex tw:items-center">
            <span class="tw:text-xl-design">
              {{ displayData?.price }}
            </span>
            <span class="tw:text-m-design">
              {{
                displayData?.volumeType === VOLUME_TYPE_ENUM.WEIGHT
                  ? '万円/kg'
                  : '円/尾'
              }}
            </span>
          </div>
        </div>
      </div>
    </q-card>
  </div>
  <q-footer
    elevated
    class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full
    tw:tl:justify-between tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-4 tw:tl:flex-row"
  >
    <BaseButton
      outline
      class="tw:rounded-[40px] tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]"
      label="入力内容を修正する"
      @click="handleBack"
    />
    <BaseButton
      outline
      class="tw:rounded-[40px] tw:bg-[#004AB9] tw:text-white
      tw:text-m-design tw:tl:w-[24.5rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]"
      label="登録する"
      @click="handleClickSubmit"
    />
  </q-footer>
</template>
<script setup>
import BaseButton from 'components/base/vs/BaseButton.vue';
import BaseLabel from 'src/components/base/vs/BaseLabel.vue';
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useAuthStore } from 'src/stores/auth-store';
import { onMounted, ref } from 'vue';
import { useAppStore } from 'src/stores/app-store';
import { useConfirmFormStore } from 'src/stores/confirm-form-store';
import { FORMAT_DATE, FORMAT_NUMBER, maskCodeString } from 'src/helpers/common';
import { SHOW_DEFAULT_SCAN_QR_ENUM, VOLUME_TYPE_ENUM } from 'src/helpers/constants';
import outboundShipmentService from 'src/shared/services/outboundShipment.service';
import dayjs from 'dayjs';
import { doParseFloatNumber } from 'src/helpers/common';
import { makeQuantityXML, makeXML } from 'src/boot/print';
import toast from 'src/shared/utilities/toast';
import MESSAGE from 'src/helpers/message';

const { proxyUser, user } = storeToRefs(useAuthStore());
const { previousRoute, settingUser } = storeToRefs(useAppStore());
const { signInProxyUser } = useAuthStore();
const displayData = ref({});
const { getConfirmData } = useConfirmFormStore();
const router = useRouter();
const INPUT_VOLUME_TYPE_OPTIONS = [
  { label: '重量', value: VOLUME_TYPE_ENUM.WEIGHT },
  { label: '尾数', value: VOLUME_TYPE_ENUM.QUANTITY },
];

// #region functions
const handleBack = () => {
  router.back();
};

const handleClickSubmit = async () => {
  // build payload for registering outbound shipment
  const payload = {
    code: displayData.value.mainCode + displayData.value.subCode,
    date: displayData.value.date,
    volume_type: displayData.value.volumeType,
    gross_weight: doParseFloatNumber(displayData.value.grossWeight),
    tare_weight: doParseFloatNumber(displayData.value.tareWeight) || 0,
    quantity: doParseFloatNumber(displayData.value.quantity),
    setting: {
      display_shipment_weight: settingUser.value.display_shipment_weight,
      price: doParseFloatNumber(displayData.value.price),
    },
    code_suffix_id: displayData.value.codeSuffixId,
    session_token: proxyUser.value?.sessionToken,
  };

  const proxyOutboundShipment =
    await outboundShipmentService.registerProxyOutboundShipment(payload);
  // print report and sign in proxy user
  printReport(proxyOutboundShipment.payload);
  signInProxyUser(null);

  // redirect to the appropriate page based on the user's settings
  toast.access(MESSAGE.MSG_RESISTER_SHIPPING_INFO);
  switch (settingUser.value.qr_scan_init) {
    case SHOW_DEFAULT_SCAN_QR_ENUM.USE_CAMERA:
      router.push({ name: 'arrivalQrCamera' });
      break;
    default:
      router.push({ name: 'arrivalQrScan' });
      break;
  }
};
// #endregion

// #region helpers functions
const maskVolumeTypeToDisplay = value =>
  INPUT_VOLUME_TYPE_OPTIONS.find(item => item.value === value)?.label || '';

const printReport = async proxyOutboundShipment => {
  const receiptNumber = settingUser.value?.receipt_number || 1;
  if (proxyOutboundShipment.setting?.price_per_kilogram) {
  const pricePerKilogram = proxyOutboundShipment.setting?.price_per_kilogram || 0;
  const price =
    Number(proxyOutboundShipment.arrival_net_weight) * Number(pricePerKilogram)*10;
  const dataPrint = makeXML(
    proxyOutboundShipment.destination_enterprise_name,
    proxyOutboundShipment.destination_user_name,
    maskCodeString(proxyOutboundShipment.code?.replaceAll('-', '')),
    FORMAT_NUMBER(proxyOutboundShipment?.arrival_gross_weight),
    FORMAT_NUMBER(proxyOutboundShipment?.arrival_tare_weight),
    FORMAT_NUMBER(proxyOutboundShipment?.arrival_net_weight),
    // map utc to local time
    dayjs(FORMAT_DATE(proxyOutboundShipment.arrival_date)).toDate(),
    pricePerKilogram || '',
    FORMAT_NUMBER(price || ''),
    proxyOutboundShipment.starting_enterprise_name,
    proxyOutboundShipment.starting_user_name,
    proxyOutboundShipment.starting_license_number || '',
    proxyOutboundShipment.starting_user_note_1 || '',
    receiptNumber
  );
  const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;

  const aTag = document.createElement('a');
  aTag.href = href;
  aTag.click();
  };

if (proxyOutboundShipment.setting?.price_per_quantity) {
  const pricePerQuantity = proxyOutboundShipment.setting?.price_per_quantity || 0;
  const price =
    Number(proxyOutboundShipment.arrival_quantity) * Number(pricePerQuantity);
  const dataPrint = makeQuantityXML(
    proxyOutboundShipment.destination_enterprise_name,
    proxyOutboundShipment.destination_user_name,
    maskCodeString(proxyOutboundShipment.code?.replaceAll('-', '')),
    FORMAT_NUMBER(proxyOutboundShipment?.arrival_quantity),
    // map utc to local time
    dayjs(FORMAT_DATE(proxyOutboundShipment.arrival_date)).toDate(),
    pricePerQuantity || '',
    FORMAT_NUMBER(price || ''),
    proxyOutboundShipment.starting_enterprise_name,
    proxyOutboundShipment.starting_user_name,
    proxyOutboundShipment.starting_license_number || '',
    proxyOutboundShipment.starting_user_note_1 || '',
    receiptNumber
  );
  const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;

  const aTag = document.createElement('a');
  aTag.href = href;
  aTag.click();
}
};
// #endregion

onMounted(() => {
  const confirmData = getConfirmData();
  if (
    confirmData &&
    previousRoute.value.name === 'registerProxyOutboundShipment'
  ) {
    displayData.value = confirmData;
  } else {
    router.push({ name: 'registerProxyOutboundShipment' });
  }
});
</script>
<style scoped></style>
