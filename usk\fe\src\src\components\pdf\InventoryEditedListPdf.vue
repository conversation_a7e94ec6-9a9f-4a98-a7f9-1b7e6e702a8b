<template>
  <div class="parentPdf tw:font-pdfSans">
    <div>
      <div id="large-body-pdf">
        <div :style="{ textAlign: 'center', fontSize: '30px' }">
          在庫修正実績
        </div>
        <div>
          <div
            :style="{
              paddingTop: '4px',
              paddingBottom: '4px',
              fontSize: '14px',
            }"
          >
            作成日：
            <span>{{ FORMAT_DATE_JAPAN() }}</span>
          </div>
          <div
            :style="{
              paddingTop: '4px',
              paddingBottom: '4px',
              fontSize: '14px',
            }"
          >
            作成者：{{ profile?.enterprise?.enterprise_name }}
          </div>
          <div
            :style="{
              paddingTop: '4px',
              paddingBottom: '4px',
              fontSize: '14px',
            }"
          >
            実績表示期間：
            <span :style="{ fontSize: '14px' }">{{
              formDataExport?.startDate
                ? FORMAT_DATE_JAPAN(formDataExport?.startDate)
                : ''
            }}</span>
            <span
              :style="{ fontSize: '14px' }"
              v-if="formDataExport?.startDate || formDataExport?.endDate"
              >～</span
            >
            <span :style="{ fontSize: '14px' }">{{
              formDataExport?.endDate
                ? FORMAT_DATE_JAPAN(formDataExport?.endDate)
                : ''
            }}</span>
          </div>
          <div
            :style="{
              paddingTop: '4px',
              paddingBottom: '4px',
              fontSize: '14px',
            }"
          >
            検索条件：
            <span :style="{ fontSize: '14px' }">
              {{ truncateText(formDataExport?.group || '', 16) }}</span
            >
            <span
              :style="{ fontSize: '14px' }"
              v-if="formDataExport?.group && formDataExport?.supplier"
              >、</span
            >
            <!-- 仕入先 supplier -->
            <span :style="{ fontSize: '14px' }">
              {{ truncateText(formDataExport?.supplier || '', 16) }}</span
            >
            <span
              :style="{ fontSize: '14px' }"
              v-if="formDataExport?.supplier && formDataExport?.enterprise_code"
              >、</span
            >
            <span :style="{ fontSize: '14px' }">
              {{ truncateText(formDataExport?.enterprise_code || '', 7) }}
            </span>
            <span
              :style="{ fontSize: '14px' }"
              v-if="formDataExport?.enterprise_code && formDataExport?.license_number"
              >、</span
            >
            <span :style="{ fontSize: '14px' }">
              {{ truncateText(formDataExport?.license_number || '', 11) }}
            </span>
          </div>
        </div>
        <div
          class="tw:flex tw:w-full tw:mt-7"
          :style="{ pageBreakInside: 'avoid' }"
        >
          <div
            class="row-item"
            :style="{ textAlign: 'center', width: '120px' }"
          >
            修正日時
          </div>
          <div
            class="row-item"
            :style="{ textAlign: 'center', width: '300px' }"
          >
            在庫ロット名
          </div>
          <div
            class="row-item"
            :style="{ textAlign: 'center', width: '150px' }"
          >
            修正前の在庫量[g]
          </div>
          <div
            class="row-item"
            :style="{ textAlign: 'center', width: '150px' }"
          >
            修正後の在庫量[g]
          </div>
          <div
            class="row-item"
            :style="{ textAlign: 'center', width: '200px' }"
          >
            修正理由
          </div>
          <div
            class="row-item"
            :style="{ textAlign: 'center', width: '200px' }"
          >
            修正内容詳細
          </div>
        </div>
        <div
          v-for="(item, index) in dataAll"
          :key="index"
          class="tw:flex tw:w-full"
          :style="{ pageBreakInside: 'avoid' }"
        >
          <div
            class="row-item"
            :style="{ textAlign: 'center', width: '120px' }"
          >
            {{ FORMAT_DATE(item.createdOn) }}
          </div>

          <div
            class="row-item"
            :style="{
              textAlign: 'left',
              width: '300px',
              whiteSpace: 'pre-wrap',
            }"
          >
            {{ wrapFullWidth(item.title, 20) }}
          </div>

          <div class="row-item" :style="{ textAlign: 'right', width: '150px' }">
            {{ item.netWeightInventory }}
          </div>

          <div class="row-item" :style="{ textAlign: 'right', width: '150px' }">
            {{ item.newNetWeightInventory }}
          </div>
          <div
            class="row-item"
            :style="{
              textAlign: 'left',
              width: '200px',
              whiteSpace: 'pre-wrap',
            }"
          >
            {{ item.typeDiff }}
          </div>
          <div
            class="row-item"
            :style="{
              textAlign: 'left',
              width: '200px',
              whiteSpace: 'pre-wrap',
            }"
          >
            {{ wrapFullWidth(item.reasonDiff, 16) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, onMounted, ref } from 'vue';
import { FORMAT_DATE, FORMAT_NUMBER, FORMAT_DATE_JAPAN } from 'helpers/common';
import profileService from 'services/profile.service';
import { TYPE_DIFFERENCE_WEIGHT_ENUM } from 'src/helpers/constants';

// Helper function to truncate text with ellipsis
const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength) + '...';
};

const dataProp = inject('inventoryEditedListExportPdf');
const formDataExport = inject('formDataExport');
const profile = ref({});
const dataAll = computed(() => dataProp.value || []);

const wrapFullWidth = (text, maxCharsPerLine = 20) => {
  if (!text) {
    return '';
  }
  const result = [];
  let currentLine = '';
  let fullWidthCount = 0;

  for (const ch of text) {
    const isFull = ch.charCodeAt(0) > 255;
    fullWidthCount += isFull ? 1 : 0.5;
    currentLine += ch;

    if (fullWidthCount >= maxCharsPerLine) {
      result.push(currentLine);
      currentLine = '';
      fullWidthCount = 0;
    }
  }

  if (currentLine) {
    result.push(currentLine);
  }
  return result.join('\n');
};

onMounted(async () => {
  const result = await profileService.getProfile();
  profile.value = result.payload.data;
});
</script>

<style scoped>
.parentPdf {
  font-size: 100% !important;
  visibility: hidden;
  position: absolute;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 32px;
}

th,
td,
.row-item {
  border: 1px solid #000;
  padding-left: 8px !important;
  padding-right: 8px !important;
  padding-top: 4px !important;
  padding-bottom: 16px !important;
  font-weight: 400;
  vertical-align: middle;
  font-size: 12px !important;
}

tr {
  page-break-inside: avoid;
}

th {
  background-color: #fff;
  text-align: 'center';
}

/* Summary section styling */
.row-item[style*='backgroundColor'] {
  background-color: #f5f5f5 !important;
  font-weight: 600;
}
</style>
