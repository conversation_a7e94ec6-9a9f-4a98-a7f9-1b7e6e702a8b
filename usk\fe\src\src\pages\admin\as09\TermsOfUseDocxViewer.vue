<!-- eslint-disable max-len -->
<template>
  <div class="tw:mt-2 tw:tl:px-3 tw:h-full" id="header">
    <iframe :src="srcTermsOfUse" frameborder='0' class="tw:w-full tw:h-[60vh]" scrolling="no">
    </iframe>
  </div>
  <!-- end content -->
</template>
<script setup>
// open page denso
import { onMounted, ref } from 'vue';

// ===== REF =====
const srcTermsOfUse = ref('');
onMounted(() => {
  srcTermsOfUse.value = `https://view.officeapps.live.com/op/embed.aspx?src=${process.env.CLOUDFRONT}/document/adminsite/termsofuse.docx`;
});
</script>
